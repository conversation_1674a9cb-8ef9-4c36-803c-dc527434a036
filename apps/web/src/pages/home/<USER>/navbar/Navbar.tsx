import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Logo } from "@/components/ui/logo";
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuList,
  NavigationMenuTrigger,
} from "@/components/ui/navigation-menu";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { ThemeToggle } from "@/components/ui/theme-toggle";
import { useAuth } from "@/hooks/useAuth";
import { useNotifications } from "@/hooks/useNotifications";
import { cn } from "@/lib/utils";
import { motion } from "framer-motion";
import {
  Bell,
  Building,
  Check,
  ChevronRight,
  LogOut,
  Settings,
} from "lucide-react";
import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { ListItem } from "./ListItem";
import { MenuCloseButton } from "./MenuComponents";
import { PricingItem } from "./PricingItem";
import { company, features, plans, resources, solutions } from "./data";

const Navbar = () => {
  const navigate = useNavigate();
  const [activeItem, setActiveItem] = useState<string | null>(null);
  const { user, signOut, isLoading } = useAuth();
  const { unreadCount, notifications, markAsRead, markAllAsRead } =
    useNotifications();

  const userInitials =
    user?.user_metadata?.first_name && user?.user_metadata?.last_name
      ? `${user.user_metadata.first_name[0]}${user.user_metadata.last_name[0]}`
      : user?.email?.substring(0, 2).toUpperCase() || "U";

  const handleNavigate = (path: string) => {
    setActiveItem(null);
    navigate(path);
  };

  const triggerClasses =
    "group inline-flex h-9 w-max items-center justify-center rounded-md bg-transparent px-3 py-2 text-sm font-medium transition-colors hover:bg-accent/50 hover:text-accent-foreground focus:bg-accent/50 focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50";
  const contentClasses = "p-4 text-popover-foreground";

  return (
    <motion.header
      initial={{ y: -20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="fixed top-0 left-0 right-0 z-50 border-b bg-background/90 backdrop-blur-lg dark:border-emerald-900/20 dark:bg-background/95"
    >
      <div className="container mx-auto px-4 h-16 flex items-center justify-between">
        <div className="flex items-center gap-8">
          <Logo size="md" showText={true} />

          <NavigationMenu
            onValueChange={setActiveItem}
            value={activeItem || undefined}
          >
            <NavigationMenuList className="gap-1">
              {/* Features Menu */}
              <NavigationMenuItem value="features" className="relative">
                <NavigationMenuTrigger className={triggerClasses}>
                  Features
                </NavigationMenuTrigger>
                <NavigationMenuContent
                  className={cn(contentClasses, "w-[750px]")}
                >
                  <div className="w-full">
                    <div className="mb-3 flex justify-between">
                      <div>
                        <h3 className="text-lg font-medium mb-1 text-foreground">
                          Platform Features
                        </h3>
                        <p className="text-sm text-muted-foreground">
                          Powerful tools for modern healthcare providers
                        </p>
                      </div>
                      <MenuCloseButton onClick={() => setActiveItem(null)} />
                    </div>
                    <ul className="grid grid-cols-3 gap-2">
                      {features.map((feature) => (
                        <ListItem
                          key={feature.title}
                          href={feature.href}
                          icon={feature.icon}
                          title={feature.title}
                          description={feature.description}
                          onClick={() => handleNavigate(feature.href)}
                        />
                      ))}
                    </ul>
                    <div className="mt-4 pt-4 border-t border-border/20">
                      <a
                        href="/features"
                        className="flex items-center gap-2 text-sm text-primary hover:underline group"
                        onClick={(e) => {
                          e.preventDefault();
                          handleNavigate("/features");
                        }}
                      >
                        View all features
                        <ChevronRight className="w-4 h-4 transition-transform group-hover:translate-x-0.5" />
                      </a>
                    </div>
                  </div>
                </NavigationMenuContent>
              </NavigationMenuItem>

              {/* Solutions Menu */}
              <NavigationMenuItem value="solutions" className="relative">
                <NavigationMenuTrigger className={triggerClasses}>
                  Solutions
                </NavigationMenuTrigger>
                <NavigationMenuContent
                  className={cn(contentClasses, "w-[600px]")}
                >
                  <div className="w-full">
                    <div className="mb-3 flex justify-between">
                      <div>
                        <h3 className="text-lg font-medium mb-1 text-foreground">
                          Healthcare Solutions
                        </h3>
                        <p className="text-sm text-muted-foreground">
                          Tailored for specific healthcare needs
                        </p>
                      </div>
                      <MenuCloseButton onClick={() => setActiveItem(null)} />
                    </div>
                    <ul className="grid grid-cols-2 gap-2">
                      {solutions.map((solution) => (
                        <ListItem
                          key={solution.title}
                          href={solution.href}
                          icon={solution.icon}
                          title={solution.title}
                          description={solution.description}
                          onClick={() => handleNavigate(solution.href)}
                        />
                      ))}
                    </ul>
                  </div>
                </NavigationMenuContent>
              </NavigationMenuItem>

              {/* Resources Menu */}
              <NavigationMenuItem value="resources" className="relative">
                <NavigationMenuTrigger className={triggerClasses}>
                  Resources
                </NavigationMenuTrigger>
                <NavigationMenuContent
                  className={cn(contentClasses, "w-[600px]")}
                >
                  <div className="w-full">
                    <div className="mb-3 flex justify-between">
                      <div>
                        <h3 className="text-lg font-medium mb-1 text-foreground">
                          Resources & Support
                        </h3>
                        <p className="text-sm text-muted-foreground">
                          Learn and get help with Spritely
                        </p>
                      </div>
                      <MenuCloseButton onClick={() => setActiveItem(null)} />
                    </div>
                    <ul className="grid grid-cols-2 gap-2">
                      {resources.map((resource) => (
                        <ListItem
                          key={resource.title}
                          href={resource.href}
                          icon={resource.icon}
                          title={resource.title}
                          description={resource.description}
                          onClick={() => handleNavigate(resource.href)}
                        />
                      ))}
                    </ul>
                  </div>
                </NavigationMenuContent>
              </NavigationMenuItem>

              {/* Company Menu */}
              <NavigationMenuItem value="company" className="relative">
                <NavigationMenuTrigger className={triggerClasses}>
                  Company
                </NavigationMenuTrigger>
                <NavigationMenuContent
                  className={cn(contentClasses, "w-[400px]")}
                >
                  <div className="w-full">
                    <div className="mb-3 flex justify-between">
                      <div>
                        <h3 className="text-lg font-medium mb-1 text-foreground">
                          About Spritely
                        </h3>
                        <p className="text-sm text-muted-foreground">
                          Learn about our mission and team
                        </p>
                      </div>
                      <MenuCloseButton onClick={() => setActiveItem(null)} />
                    </div>
                    <ul className="flex flex-col gap-2">
                      {company.map((item) => (
                        <ListItem
                          key={item.title}
                          href={item.href}
                          icon={item.icon}
                          title={item.title}
                          description={item.description}
                          onClick={() => handleNavigate(item.href)}
                        />
                      ))}
                    </ul>
                  </div>
                </NavigationMenuContent>
              </NavigationMenuItem>

              {/* Pricing Menu */}
              <NavigationMenuItem value="pricing" className="relative">
                <NavigationMenuTrigger className={triggerClasses}>
                  Pricing
                </NavigationMenuTrigger>
                <NavigationMenuContent
                  className={cn(contentClasses, "w-[600px]")}
                >
                  <div className="w-full">
                    <div className="mb-3 flex justify-between">
                      <div>
                        <h3 className="text-lg font-medium mb-1 text-foreground">
                          Pricing Plans
                        </h3>
                        <p className="text-sm text-muted-foreground">
                          Find the right plan for your practice
                        </p>
                      </div>
                      <MenuCloseButton onClick={() => setActiveItem(null)} />
                    </div>

                    <ul className="grid grid-cols-3 gap-2">
                      {plans.map((plan) => (
                        <PricingItem
                          key={plan.name}
                          title={plan.name}
                          description={plan.description}
                          features={plan.features}
                          onClick={() => handleNavigate("/pricing")}
                        />
                      ))}
                    </ul>

                    <div className="mt-4 pt-4 border-t border-border/20">
                      <a
                        href="/pricing"
                        className="flex items-center gap-2 text-sm text-primary hover:underline group"
                        onClick={(e) => {
                          e.preventDefault();
                          handleNavigate("/pricing");
                        }}
                      >
                        Compare all plans
                        <ChevronRight className="w-4 h-4 transition-transform group-hover:translate-x-0.5" />
                      </a>
                    </div>
                  </div>
                </NavigationMenuContent>
              </NavigationMenuItem>
            </NavigationMenuList>
          </NavigationMenu>
        </div>

        <div className="flex items-center gap-4">
          <ThemeToggle />

          {isLoading ? (
            // Loading state - show skeleton placeholders
            <div className="flex items-center gap-4">
              <div className="w-8 h-8 bg-muted rounded-full animate-pulse" />
              <div className="w-8 h-8 bg-muted rounded-full animate-pulse" />
            </div>
          ) : user ? (
            <>
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="ghost" size="icon" className="relative">
                    <Bell className="h-5 w-5" />
                    {unreadCount > 0 && (
                      <span className="absolute top-1 right-1 flex items-center justify-center min-w-[14px] h-[14px] text-[10px] font-bold bg-red-500 text-white rounded-full px-1">
                        {unreadCount > 9 ? "9+" : unreadCount}
                      </span>
                    )}
                    <span className="sr-only">
                      Notifications ({unreadCount})
                    </span>
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80 p-0" align="end" sideOffset={6}>
                  <div className="flex items-center justify-between p-4 border-b">
                    <h3 className="font-medium">Notifications</h3>
                    {unreadCount > 0 && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 text-xs"
                        onClick={(e) => {
                          e.stopPropagation();
                          markAllAsRead();
                        }}
                      >
                        <Check className="mr-1 h-3 w-3" />
                        Mark all as read
                      </Button>
                    )}
                  </div>
                  <div className="max-h-80 overflow-y-auto">
                    {notifications.length === 0 ? (
                      <div className="p-4 text-center text-sm text-muted-foreground">
                        No notifications
                      </div>
                    ) : (
                      <div className="flex flex-col">
                        {notifications.map((notification) => (
                          <div
                            key={notification.id}
                            className={cn(
                              "p-4 border-b last:border-b-0 cursor-pointer hover:bg-accent/50 transition-colors",
                              !notification.read && "bg-accent/20",
                            )}
                            onClick={(e) => {
                              e.stopPropagation();
                              markAsRead(notification.id);
                            }}
                          >
                            <div className="flex items-start gap-2">
                              <div
                                className={cn(
                                  "w-2 h-2 mt-1.5 rounded-full",
                                  notification.type === "info" && "bg-blue-500",
                                  notification.type === "success" &&
                                    "bg-green-500",
                                  notification.type === "warning" &&
                                    "bg-yellow-500",
                                  notification.type === "error" && "bg-red-500",
                                )}
                              />
                              <div className="flex-1">
                                <p className="text-sm font-medium">
                                  {notification.title}
                                </p>
                                <p className="text-xs text-muted-foreground mt-1">
                                  {notification.message}
                                </p>
                                <p className="text-xs text-muted-foreground/70 mt-2">
                                  {new Date(
                                    notification.created_at,
                                  ).toLocaleTimeString([], {
                                    hour: "2-digit",
                                    minute: "2-digit",
                                  })}
                                </p>
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>
                </PopoverContent>
              </Popover>

              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="ghost"
                    className="relative h-8 w-8 rounded-full"
                  >
                    <Avatar className="h-8 w-8">
                      <AvatarImage
                        src={user?.user_metadata?.avatar_url || ""}
                        alt={user?.user_metadata?.full_name || "User"}
                      />
                      <AvatarFallback>{userInitials}</AvatarFallback>
                    </Avatar>
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-56" align="end" forceMount>
                  <DropdownMenuLabel className="font-normal">
                    <div className="flex flex-col space-y-1">
                      <p className="text-sm font-medium leading-none">
                        {user?.user_metadata?.first_name
                          ? `${user.user_metadata.first_name} ${user.user_metadata.last_name}`
                          : user?.email}
                      </p>
                      <p className="text-xs leading-none text-muted-foreground">
                        {user?.email}
                      </p>
                    </div>
                  </DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => navigate("/dashboard")}>
                    <Building className="mr-2 h-4 w-4" />
                    <span>Dashboard</span>
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => navigate("/settings")}>
                    <Settings className="mr-2 h-4 w-4" />
                    <span>Settings</span>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem onClick={() => signOut()}>
                    <LogOut className="mr-2 h-4 w-4" />
                    <span>Log out</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </>
          ) : (
            <>
              <Button
                variant="ghost"
                className="text-sm hover:bg-gradient-to-r hover:from-emerald-500/10 hover:to-blue-600/10 transition-all duration-200"
                onClick={() => navigate("/login")}
              >
                Sign In
              </Button>
              <Button
                className="bg-gradient-to-r from-emerald-500 to-blue-600 text-black font-medium hover:opacity-90 shadow-sm"
                onClick={() => navigate("/register")}
              >
                Get Started
              </Button>
            </>
          )}
        </div>
      </div>
    </motion.header>
  );
};

export default Navbar;

import { Organization } from "@/contexts/auth-context-types";
import { useAuth } from "@/hooks/useAuth";
import {
  cacheOrganizationData,
  clearOrganizationCache,
} from "@/lib/auth/organization-cache";
import { supabase } from "@/lib/supabase";
import { useEffect, useState } from "react";
import { toast } from "sonner";

// UI Components
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import secureStorage from "@/lib/secure-storage";
import { Building2, Loader2, LogOut, Plus } from "lucide-react";
import { useNavigate } from "react-router-dom";

export function FullSetupPage() {
  const { user, signOut, setOrganization } = useAuth();
  const navigate = useNavigate();
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isCreating, setIsCreating] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [orgName, setOrgName] = useState("");
  const [orgType, setOrgType] = useState("personal");

  // Initialize organization name based on user data
  useEffect(() => {
    if (user?.user_metadata?.first_name) {
      setOrgName(`${user.user_metadata.first_name}'s Practice`);
    } else if (user?.email) {
      const emailName = user.email.split("@")[0];
      setOrgName(`${emailName}'s Practice`);
    } else {
      setOrgName("My Healthcare Practice");
    }
  }, [user]);

  // Fetch existing organizations
  useEffect(() => {
    async function fetchOrganizations() {
      if (!user) {
        setIsLoading(false);
        return;
      }

      // If we already have organizations in secure storage, use them
      try {
        const keys = Object.keys(sessionStorage);
        const orgKeys = keys.filter(
          (key) =>
            key.startsWith("spritely_secure_org_") &&
            key.includes(user.id),
        );

        if (orgKeys.length > 0) {
          // Try to parse the organizations from secure storage
          const orgs: Organization[] = [];

          for (const key of orgKeys) {
            try {
              const orgData = secureStorage.get(key.replace("spritely_secure_", ""));
              if (orgData && typeof orgData === 'object' && 'id' in orgData && 'name' in orgData) {
                orgs.push(orgData as Organization);
              }
            } catch (_e) {
              console.error("Error fetching organizations:", _e);
            }
          }

          if (orgs.length > 0) {
            setOrganizations(orgs);
            setIsLoading(false);
            return;
          }
        }
      } catch (_e) {
        console.error("Error fetching organizations:", _e);
      }

      try {
        setIsLoading(true);
        setErrorMessage("");

        // First, get user roles
        const { data: userRoles, error: rolesError } = await supabase
          .from("user_roles")
          .select("*")
          .eq("user_id", user.id);

        if (rolesError) {
          console.error("Error checking user roles:", rolesError.message);
          setErrorMessage("Failed to check user roles");
          setIsLoading(false);
          return;
        }

        // Skip logging user roles

        // If no roles, no organizations
        if (!userRoles || userRoles.length === 0) {
          setOrganizations([]);
          setIsLoading(false);
          return;
        }

        // Get organizations for each role
        const orgIds = userRoles
          .map((role) => role.organization_id)
          .filter((id): id is string => Boolean(id));

        if (orgIds.length === 0) {
          setOrganizations([]);
          setIsLoading(false);
          return;
        }

        const { data: orgs, error: orgsError } = await supabase
          .from("organizations")
          .select("*")
          .in("id", orgIds);

        if (orgsError) {
          console.error("Error fetching organizations:", orgsError.message);
          setErrorMessage("Failed to load organizations");
          setIsLoading(false);
          return;
        }

        setOrganizations(orgs || []);
      } catch (err) {
        console.error("Exception fetching organizations:", err);
        setErrorMessage("Failed to load organizations");
      } finally {
        setIsLoading(false);
      }
    }

    fetchOrganizations();
  }, [user]);

  // Handle organization selection
  const handleSelectOrganization = async (org: Organization) => {
    try {
      setIsLoading(true);
      const userId = user?.id;
      if (!userId) {
        throw new Error("User ID is missing");
      }

      // First, clear any existing organization cache
      clearOrganizationCache(userId);

      // Create a user_role if it doesn't exist already
      const { data: existingRole, error: roleCheckError } = await supabase
        .from("user_roles")
        .select("id")
        .eq("user_id", userId)
        .eq("organization_id", org.id)
        .maybeSingle();

      if (roleCheckError) {
        console.error("Error checking existing role:", roleCheckError);
      }

      // If the user doesn't have a role for this organization, create one
      if (!existingRole) {
        const { error: roleError } = await supabase.from("user_roles").insert([
          {
            user_id: userId,
            organization_id: org.id,
            role: "org_admin", // Default role for organization selection
          },
        ]);

        if (roleError) {
          console.error("Error creating user role:", roleError);
          throw new Error("Failed to assign user to organization");
        }
      }

      // Use the setOrganization function from the auth context
      // This will properly update the auth state and cache the organization
      setOrganization(org);

      // Show success message
      toast.success("Organization selected successfully");

      // Use our improved caching function to ensure the organization is persisted
      try {
        // Cache the organization with isLastSelected=true to mark it as the active organization
        cacheOrganizationData(userId, org, { isLastSelected: true });
      } catch (err) {
        console.warn("Failed to save organization to local storage:", err);

        // Fallback to direct secure storage if the caching fails
        try {
          secureStorage.setOrganizationData(`org_${userId}`, org);
        } catch (fallbackErr) {
          console.error("Even fallback storage failed:", fallbackErr);
        }
      }

      // First try React Router navigation
      navigate("/dashboard", { replace: true });

      // As a fallback, also use window.location after a delay
      // This ensures the redirect happens even if there's an issue with React Router
      setTimeout(() => {
        if (window.location.pathname !== "/dashboard") {
          window.location.href = "/dashboard";
        }
      }, 1000);
    } catch (err) {
      console.error("Error selecting organization:", err);
      setErrorMessage("Failed to select organization");
      setIsLoading(false);
    }
  };

  // Handle organization creation
  const handleCreateOrganization = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user || !orgName.trim()) return;

    try {
      setIsCreating(true);
      setErrorMessage("");

      // Step 1: Create the organization
      const { data: newOrg, error: createError } = await supabase
        .from("organizations")
        .insert([
          {
            name: orgName.trim(),
            type: orgType,
            settings: {},
          },
        ])
        .select()
        .single();

      if (createError) {
        console.error("Error creating organization:", createError);
        setErrorMessage("Failed to create organization");
        return;
      }

      // Step 2: Create the user_role
      const { error: roleError } = await supabase.from("user_roles").insert([
        {
          user_id: user.id,
          organization_id: newOrg.id,
          role: "org_admin",
        },
      ]);

      if (roleError) {
        console.error("Error creating user role:", roleError);
        setErrorMessage("Failed to assign user to organization");
        return;
      }

      // Show success message
      toast.success("Organization created successfully!");

      // Select the new organization
      handleSelectOrganization(newOrg);
    } catch (err) {
      console.error("Error creating organization:", err);
      setErrorMessage("Failed to create organization");
    } finally {
      setIsCreating(false);
    }
  };

  // Handle sign out
  const handleSignOut = async () => {
    try {
      // Clear secure storage first to ensure all caches are cleared
      secureStorage.clear();

      // Then call the signOut function - let NavigationManager handle the redirect
      await signOut();

      // Don't manually navigate - let NavigationManager handle it
      console.debug("[SETUP] Sign out completed, NavigationManager will handle redirect");
    } catch (error) {
      console.error("Error signing out:", error);
      toast.error("Failed to sign out");
    }
  };

  return (
    <div className="flex h-full items-center justify-center bg-background p-4 no-scrollbar">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-2xl">Organization Setup</CardTitle>
          <CardDescription>
            {organizations.length > 0
              ? "Select an organization or create a new one"
              : "Create your first organization to get started"}
          </CardDescription>
        </CardHeader>

        <CardContent className="space-y-6">
          {errorMessage && (
            <Alert variant="destructive">
              <AlertDescription>{errorMessage}</AlertDescription>
            </Alert>
          )}

          {isLoading ? (
            <div className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          ) : (
            <>
              {organizations.length > 0 && (
                <div className="space-y-3">
                  <h3 className="font-medium">Your Organizations</h3>
                  <div className="rounded-md border">
                    {organizations.map((org) => (
                      <div
                        key={org.id}
                        className="flex items-center justify-between border-b p-3 last:border-0 hover:bg-accent/50"
                      >
                        <div className="flex items-center gap-3">
                          <Building2 className="h-5 w-5 text-muted-foreground" />
                          <div>
                            <p className="font-medium">{org.name}</p>
                            <p className="text-xs text-muted-foreground">
                              {org.type}
                            </p>
                          </div>
                        </div>
                        <Button
                          onClick={() => handleSelectOrganization(org)}
                          size="sm"
                        >
                          Select
                        </Button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <form onSubmit={handleCreateOrganization} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="orgName">Organization Name</Label>
                  <Input
                    id="orgName"
                    value={orgName}
                    onChange={(e) => setOrgName(e.target.value)}
                    placeholder="Enter organization name"
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="orgType">Organization Type</Label>
                  <select
                    id="orgType"
                    value={orgType}
                    onChange={(e) => setOrgType(e.target.value)}
                    className="flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-xs file:border-0 file:bg-transparent file:text-sm file:font-medium focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
                  >
                    <option value="personal">Personal Practice</option>
                    <option value="clinic">Clinic</option>
                    <option value="hospital">Hospital</option>
                    <option value="specialty">Specialty Practice</option>
                  </select>
                </div>

                <Button type="submit" className="w-full" disabled={isCreating}>
                  {isCreating ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    <>
                      <Plus className="mr-2 h-4 w-4" />
                      {organizations.length > 0
                        ? "Create New Organization"
                        : "Create Organization"}
                    </>
                  )}
                </Button>
              </form>
            </>
          )}
        </CardContent>

        <CardFooter className="flex justify-center border-t pt-6">
          <Button
            variant="outline"
            onClick={handleSignOut}
            className="flex items-center gap-2"
          >
            <LogOut className="h-4 w-4" />
            Sign Out
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}

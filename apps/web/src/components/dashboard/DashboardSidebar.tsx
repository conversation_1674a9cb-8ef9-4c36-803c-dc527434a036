import { But<PERSON> } from "@/components/ui/button";
import { useAuth } from "@/hooks/useAuth";
import { cn } from "@/lib/utils";
import {
  BarChart,
  Bell,
  Calendar,
  ChevronLeft,
  ChevronRight,
  ClipboardList,
  FileText,
  LayoutDashboard,
  MessageSquare,
  Pill,
  Settings, Users
} from "lucide-react";
import { useLocation, useNavigate } from "react-router-dom";
import { Logo } from "../ui/logo";

interface DashboardSidebarProps {
  open: boolean;
  setOpen: (open: boolean) => void;
}

export function DashboardSidebar({ open, setOpen }: DashboardSidebarProps) {
  const navigate = useNavigate();
  const location = useLocation();
  const { organization } = useAuth();

  const navItems = [
    {
      name: "Dashboard",
      href: "/dashboard",
      icon: LayoutDashboard,
    },
    {
      name: "Patients",
      href: "/patients",
      icon: Users,
    },
    {
      name: "Appointments",
      href: "/appointments",
      icon: Calendar,
    },
    {
      name: "Medical Records",
      href: "/medical-records",
      icon: ClipboardList,
    },
    {
      name: "Medications",
      href: "/medications",
      icon: Pill,
    },
    {
      name: "Documents",
      href: "/documents",
      icon: FileText,
    },
    {
      name: "Messages",
      href: "/messages",
      icon: MessageSquare,
    },
    {
      name: "Notifications",
      href: "/notifications",
      icon: Bell,
    },
    {
      name: "Analytics",
      href: "/analytics",
      icon: BarChart,
    },
    {
      name: "Settings",
      href: "/settings",
      icon: Settings,
    },
  ];

  return (
    <div
      className={cn(
        "h-screen bg-sidebar border-r border-border transition-all duration-300 flex flex-col flex-shrink-0",
        open ? "w-64" : "w-20",
      )}
    >
      {/* Logo and toggle */}
      <div className="p-4 border-b border-border flex items-center justify-between">
        <Logo 
          size="md" 
          showText={open} 
          className="flex-shrink-0"
        />
        <Button
          variant="ghost"
          size="icon"
          className="h-8 w-8"
          onClick={() => setOpen(!open)}
        >
          {open ? <ChevronLeft size={18} /> : <ChevronRight size={18} />}
        </Button>
      </div>

      {/* Organization name */}
      {organization && (
        <div
          className={cn(
            "px-4 py-3 border-b border-border",
            !open && "flex justify-center",
          )}
        >
          {open ? (
            <div>
              <p className="font-medium text-sm">{organization.name}</p>
              <p className="text-xs text-muted-foreground">
                {organization.type || "Healthcare Organization"}
              </p>
            </div>
          ) : (
            <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
              <span className="text-xs font-medium text-primary">
                {organization.name.charAt(0)}
              </span>
            </div>
          )}
        </div>
      )}

      {/* Navigation */}
      <nav className="flex-1 overflow-y-auto py-4">
        <ul className="space-y-1 px-2">
          {navItems.map((item) => {
            const isActive = location.pathname === item.href;
            return (
              <li key={item.href}>
                <Button
                  variant={isActive ? "default" : "ghost"}
                  className={cn(
                    "w-full justify-start",
                    !open && "justify-center px-0",
                    isActive && "bg-primary text-primary-foreground",
                  )}
                  onClick={() => navigate(item.href)}
                >
                  <item.icon className={cn("h-5 w-5", open && "mr-2")} />
                  {open && <span>{item.name}</span>}
                </Button>
              </li>
            );
          })}
        </ul>
      </nav>

      {/* Footer */}
      <div className="p-4 border-t border-border">
        <Button
          variant="outline"
          className={cn("w-full", !open && "justify-center px-0")}
          onClick={() => navigate("/help")}
        >
          <Bell className={cn("h-5 w-5", open && "mr-2")} />
          {open && <span>Help & Support</span>}
        </Button>
      </div>
    </div>
  );
}

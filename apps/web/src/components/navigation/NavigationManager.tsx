import { useAuth } from "@/hooks/useAuth";
import { useUserRoles } from "@/hooks/useUserRoles";
import { useOrganizationStore } from "@/stores/organization-store";
import { Navigate, useLocation } from "react-router-dom";

type NavigationManagerProps = {
  children: React.ReactNode;
};

export function NavigationManager({ children }: NavigationManagerProps) {
  const { user, organization, isLoading } = useAuth();
  const { isSystemAdmin } = useUserRoles();
  const { currentOrg } = useOrganizationStore();
  const location = useLocation();

  // Don't redirect during initial loading
  if (isLoading) {
    return <>{children}</>;
  }

  const currentPath = location.pathname;
  const effectiveOrganization = organization || currentOrg;

  // PUBLIC ROUTES - always accessible
  const publicRoutes = ["/", "/login", "/register", "/forgot-password", "/reset-password"];

  // AUTH ROUTES - redirect authenticated users away from these
  const authRoutes = ["/login", "/register", "/forgot-password", "/reset-password"];

  // PROTECTED ROUTES - require authentication
  const protectedRoutes = ["/dashboard", "/settings", "/profile", "/organizations", "/patients"];
  const protectedPatterns = ["/patients/", "/organizations/"];

  const isProtectedRoute = protectedRoutes.includes(currentPath) ||
    protectedPatterns.some(pattern => currentPath.startsWith(pattern));

  // RULE 1: If user is logged in and on auth page, redirect to dashboard
  if (user && isAuthRoute) {
    console.debug(`[NAV] Authenticated user on auth page, redirecting to dashboard`);
    return <Navigate to="/dashboard" replace />;
  }

  // RULE 2: If user is not logged in and on protected route, redirect to login
  if (!user && isProtectedRoute) {
    console.debug(`[NAV] Unauthenticated user on protected route, redirecting to login`);
    return <Navigate to="/login" replace />;
  }

  // RULE 3: If user is logged in but no organization and on route that needs org, redirect appropriately
  if (user && !effectiveOrganization && isProtectedRoute &&
      currentPath !== "/setup" && currentPath !== "/organizations") {
    if (isSystemAdmin) {
      console.debug(`[NAV] System admin without org, redirecting to organizations`);
      return <Navigate to="/organizations" replace />;
    } else {
      console.debug(`[NAV] User without org, redirecting to setup`);
      return <Navigate to="/setup" replace />;
    }
  }

  // RULE 4: Root path redirects
  if (currentPath === "/") {
    if (user) {
      console.debug(`[NAV] Authenticated user on root, redirecting to dashboard`);
      return <Navigate to="/dashboard" replace />;
    } else {
      console.debug(`[NAV] Unauthenticated user on root, redirecting to login`);
      return <Navigate to="/login" replace />;
    }
  }

  // No redirect needed, render children
  return <>{children}</>;
}

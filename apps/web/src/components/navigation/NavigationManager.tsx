import { useAuth } from "@/hooks/useAuth";
import { useUserRoles } from "@/hooks/useUserRoles";
import { useOrganizationStore } from "@/stores/organization-store";
import { useEffect, useMemo, useRef, useState } from "react";
import { Navigate, useLocation } from "react-router-dom";

// Define route types for cleaner navigation logic
type RouteConfig = {
  public: string[];
  protected: string[];
  protectedPatterns: string[]; // Dynamic routes that start with these patterns
  special: {
    root: string;
    defaultAuthenticated: string;
    defaultUnauthenticated: string;
  };
};

type NavigationManagerProps = {
  children: React.ReactNode;
};

export function NavigationManager({ children }: NavigationManagerProps) {
  const { user, organization, isLoading } = useAuth();
  const { isSystemAdmin, isLoading: rolesLoading } = useUserRoles();
  const { isLoading: orgStoreLoading, currentOrg, availableOrgs } = useOrganizationStore();
  const location = useLocation();

  // Track if we've given the org store a chance to load for the current user
  const orgStoreInitializedRef = useRef<string | null>(null);

  // Track organization store initialization
  const orgStoreInitialized = user ? orgStoreInitializedRef.current === user.id : false;

  // Clear org store initialized ref when user changes
  if (!user) {
    orgStoreInitializedRef.current = null;
  }

  // Mark org store as initialized when we have data or when loading completes
  if (user && !orgStoreInitialized && (currentOrg || availableOrgs.length > 0 || (!orgStoreLoading && orgStoreInitializedRef.current !== user.id))) {
    orgStoreInitializedRef.current = user.id;
  }

  // Add minimal logging for debugging (only when there are issues)
  if (!organization && !currentOrg && user && !isLoading && !orgStoreLoading) {
    console.debug(`[NAV_DEBUG] No organization found:`, {
      path: location.pathname,
      user: user.email,
      orgStoreInitialized,
      availableOrgsCount: availableOrgs.length
    });
  }

  // Track redirect attempts to prevent infinite loops
  const redirectCountRef = useRef(0);
  const lastRedirectTimeRef = useRef(0);
  const lastPathRef = useRef("");

  // Track the intended route during loading to preserve it after auth/org loading
  const intendedRouteRef = useRef<string | null>(null);

  // Track loading to prevent infinite loading screens
  const [loadingStartTime] = useState(() => Date.now());
  const [shouldBypassLoading, setShouldBypassLoading] = useState(false);

  // Track render safety to prevent blank screens
  const [safetyRenderEnabled, setSafetyRenderEnabled] = useState(false);

  // Track previous user state to detect auth changes
  const prevUserRef = useRef<typeof user>(user);

  // Define routes configuration
  const routes: RouteConfig = useMemo(
    () => ({
      public: ["/login", "/register", "/reset-password", "/setup"],
      protected: [
        "/dashboard",
        "/settings",
        "/profile",
        "/organizations",
        "/patients",
      ],
      protectedPatterns: ["/patients/", "/organizations/"], // Dynamic routes that start with these patterns
      special: {
        root: "/",
        defaultAuthenticated: "/dashboard",
        defaultUnauthenticated: "/login",
      },
    }),
    [],
  );

  // Capture intended route when loading starts
  useEffect(() => {
    const currentPath = location.pathname;
    const isProtectedRoute = routes.protected.includes(currentPath) ||
      routes.protectedPatterns.some((pattern) => currentPath.startsWith(pattern));

    // If we're loading and on a protected route, capture it as the intended route
    if ((isLoading || rolesLoading || orgStoreLoading) && isProtectedRoute && user) {
      if (!intendedRouteRef.current) {
        intendedRouteRef.current = currentPath;
        console.debug(`[NAV] Captured intended route during loading: ${currentPath}`);
      }
    }

    // Clear intended route when loading is complete and we have organization
    const effectiveOrganization = organization || currentOrg;
    if (!isLoading && !rolesLoading && !orgStoreLoading && effectiveOrganization && intendedRouteRef.current) {
      console.debug(`[NAV] Clearing intended route, loading complete with org: ${effectiveOrganization.name}`);
      intendedRouteRef.current = null;
    }
  }, [location.pathname, isLoading, rolesLoading, orgStoreLoading, user, organization, currentOrg, routes]);

  // Handle immediate navigation on auth state changes
  useEffect(() => {
    const prevUser = prevUserRef.current;
    const currentUser = user;
    const currentPath = location.pathname;

    // Update the ref for next comparison
    prevUserRef.current = currentUser;

    // Skip if this is the initial render
    if (prevUser === currentUser) {
      return;
    }

    // User just logged out - force immediate redirect to login if on protected route
    if (prevUser && !currentUser && !isLoading) {
      const isPublicRoute = routes.public.includes(currentPath);
      if (!isPublicRoute) {
        console.debug(`[NAV] Auth change detected: user logged out, forcing redirect from ${currentPath}`);
        // Force immediate redirect by clearing the last path
        lastPathRef.current = "";
      }
    }

    // User just logged in - force immediate redirect from auth pages
    if (!prevUser && currentUser) {
      if (["/login", "/register", "/reset-password"].includes(currentPath)) {
        console.debug(`[NAV] Auth change detected: user logged in, forcing redirect from ${currentPath}`);
        // Force immediate redirect by clearing the last path
        lastPathRef.current = "";
      }
    }
  }, [user, isLoading, location.pathname, routes]);

  // Safety timeout to prevent infinite loading
  useEffect(() => {
    const safetyTimeout = setTimeout(() => {
      setSafetyRenderEnabled(true);
    }, 10000); // 10 seconds safety timeout

    return () => clearTimeout(safetyTimeout);
  }, []);

  // Loading bypass after timeout
  useEffect(() => {
    const loadingTimeout = setTimeout(() => {
      if (isLoading && Date.now() - loadingStartTime > 5000) {
        setShouldBypassLoading(true);
      }
    }, 5000); // 5 seconds timeout

    return () => clearTimeout(loadingTimeout);
  }, [isLoading, loadingStartTime]);

  // Handle navigation logic
  const shouldRedirect = useMemo(() => {
    const hasUser = !!user;
    const hasOrg = !!organization;
    const hasStoreOrg = !!currentOrg;
    const isStillLoading = isLoading || rolesLoading || orgStoreLoading;
    const currentPath = location.pathname;

    // If user is authenticated but org store hasn't been initialized yet, wait
    const waitingForOrgStoreInit = hasUser && !orgStoreInitialized;

    // If user is authenticated but no organization in auth context, check if store has org data
    // This handles the race condition where auth loads before org store syncs
    const waitingForOrgSync = hasUser && !hasOrg && hasStoreOrg;

    // PRIORITY 1: Handle immediate auth state changes - don't wait for org loading
    // If user just logged in and we're on a public auth page, redirect to dashboard immediately
    if (hasUser && ["/login", "/register", "/reset-password"].includes(currentPath)) {
      console.debug(`[NAV] User logged in, redirecting from auth page ${currentPath} to dashboard`);
      return routes.special.defaultAuthenticated;
    }

    // If user is null (logged out), immediately redirect to login
    if (!hasUser && !isLoading) {
      const isPublicRoute = routes.public.includes(currentPath);
      if (!isPublicRoute) {
        console.debug(`[NAV] User logged out, redirecting to login from ${currentPath}`);
        return routes.special.defaultUnauthenticated;
      }
      return null;
    }

    // Don't redirect if we're already on the target path (after auth checks)
    if (currentPath === lastPathRef.current) {
      return null;
    }

    // Update last path
    lastPathRef.current = currentPath;

    // For other navigation decisions, be more conservative and wait for loading to complete
    if ((isStillLoading || waitingForOrgStoreInit || waitingForOrgSync) && !shouldBypassLoading && !safetyRenderEnabled) {
      console.debug(`[NAV] Skipping redirect - loading or waiting: auth=${isLoading}, roles=${rolesLoading}, org=${orgStoreLoading}, waitingInit=${waitingForOrgStoreInit}, waitingSync=${waitingForOrgSync}`);
      return null;
    }

    // Handle root path
    if (currentPath === routes.special.root) {
      // If we have an intended route and user is authenticated with organization, go there
      const effectiveOrganization = organization || currentOrg;
      if (user && effectiveOrganization && intendedRouteRef.current) {
        const intended = intendedRouteRef.current;
        intendedRouteRef.current = null; // Clear it
        console.debug(`[NAV] Redirecting to intended route from root: ${intended}`);
        return intended;
      }
      return user
        ? routes.special.defaultAuthenticated
        : routes.special.defaultUnauthenticated;
    }

    // Public routes are always accessible (auth page redirects handled above)
    if (routes.public.includes(currentPath)) {
      return null;
    }

    // Protected routes require authentication
    const isProtectedRoute =
      routes.protected.includes(currentPath) ||
      routes.protectedPatterns.some((pattern) =>
        currentPath.startsWith(pattern),
      );

    if (isProtectedRoute) {
      if (!user) {
        return routes.special.defaultUnauthenticated;
      }
      // Most protected routes also require an organization, except /organizations itself
      // Check both auth organization and store organization
      const effectiveOrganization = organization || currentOrg;
      if (
        !effectiveOrganization &&
        currentPath !== "/setup" &&
        currentPath !== "/organizations"
      ) {
        // Check if we're still loading organization data to avoid premature redirects
        if (isLoading || rolesLoading || orgStoreLoading) {
          console.debug(`[NAV] Organization required but still loading - staying on ${currentPath}`);
          return null; // Don't redirect while still loading
        }

        console.debug(`[NAV] No organization found, redirecting from ${currentPath}`);
        // System admins should go to organization selection, not setup
        if (isSystemAdmin) {
          return "/organizations";
        }
        return "/setup";
      }

      // If we just finished loading and have an organization, check if we should go to intended route
      if (user && effectiveOrganization && intendedRouteRef.current && currentPath !== intendedRouteRef.current) {
        const intended = intendedRouteRef.current;
        intendedRouteRef.current = null; // Clear it
        console.debug(`[NAV] Redirecting to intended route after org load: ${intended}`);
        return intended;
      }

      return null;
    }

    // Default to dashboard for authenticated users, login for others
    // But if we have an intended route and user is authenticated with organization, go there
    const effectiveOrganization = organization || currentOrg;
    if (user && effectiveOrganization && intendedRouteRef.current) {
      const intended = intendedRouteRef.current;
      intendedRouteRef.current = null; // Clear it
      console.debug(`[NAV] Redirecting to intended route from default: ${intended}`);
      return intended;
    }

    return user
      ? routes.special.defaultAuthenticated
      : routes.special.defaultUnauthenticated;
  }, [
    user,
    organization,
    currentOrg,
    location.pathname,
    isLoading,
    rolesLoading,
    orgStoreLoading,
    orgStoreInitialized,
    shouldBypassLoading,
    safetyRenderEnabled,
    routes,
    isSystemAdmin,
  ]);

  // Handle the actual redirect
  if (shouldRedirect) {
    console.debug(`[NAV] Redirecting from ${location.pathname} to ${shouldRedirect}`, {
      user: !!user,
      organization: !!organization,
      currentOrg: !!currentOrg,
      isLoading,
      rolesLoading,
      orgStoreLoading
    });

    // Prevent infinite redirects
    const now = Date.now();
    if (now - lastRedirectTimeRef.current < 1000) {
      redirectCountRef.current++;
      if (redirectCountRef.current > 5) {
        console.error("Too many redirects, rendering current page");
        return <>{children}</>;
      }
    } else {
      redirectCountRef.current = 0;
    }
    lastRedirectTimeRef.current = now;

    return <Navigate to={shouldRedirect} replace />;
  }

  // NavigationManager at app level should not render loading screens
  // LoadingStateManager and other components handle loading states
  // Just render children - navigation redirects happen above
  return <>{children}</>;
}

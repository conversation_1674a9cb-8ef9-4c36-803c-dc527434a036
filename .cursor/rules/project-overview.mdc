---
description:
globs:
alwaysApply: false
---
# Spritely Healthcare Application Overview

## Project Structure
This is a monorepo healthcare management application built with React, TypeScript, and Supabase. The main web application is located in `apps/web/`.

## Key Technologies
- **Frontend**: React 18, TypeScript, Vite
- **Backend**: Supabase (PostgreSQL + Auth + RLS)
- **UI**: Tailwind CSS, Radix UI components
- **State Management**: React Context (Auth, Organization)
- **Routing**: React Router v6
- **Build Tool**: Turbo (monorepo), Vite (dev server)

## Main Application Entry Points
- [apps/web/src/main.tsx](mdc:apps/web/src/main.tsx) - Application entry point
- [apps/web/src/App.tsx](mdc:apps/web/src/App.tsx) - Main app component with routing
- [apps/web/src/components/navigation/NavigationManager.tsx](mdc:apps/web/src/components/navigation/NavigationManager.tsx) - Handles route protection and navigation logic

## Core Features
- **Patient Management**: CRUD operations for patient records
- **Organization Management**: Multi-tenant organization support
- **User Roles**: System admin, org admin, regular users with RLS
- **Authentication**: Supabase Auth with role-based access control
- **Dashboard**: Activity feeds, patient statistics, recent activity

## Database Schema
- Located in `supabase/migrations/` with the main schema in [supabase/migrations/20250524023305_initial_schema.sql](mdc:supabase/migrations/20250524023305_initial_schema.sql)
- Key tables: `patients`, `organizations`, `user_roles`, `appointments`, `medical_records`
- Row Level Security (RLS) policies for multi-tenant data isolation

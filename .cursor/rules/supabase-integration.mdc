---
description:
globs:
alwaysApply: false
---
# Supabase Integration Patterns

## Database Configuration
- [supabase/config.toml](mdc:supabase/config.toml) - Local Supabase configuration
- [apps/web/src/lib/supabase.ts](mdc:apps/web/src/lib/supabase.ts) - Supabase client initialization
- Environment variables: `VITE_SUPABASE_URL`, `VITE_SUPABASE_ANON_KEY`

## Schema Management
- [supabase/migrations/20250524023305_initial_schema.sql](mdc:supabase/migrations/20250524023305_initial_schema.sql) - Main database schema
- Type generation: `npm run generate-types` - Updates TypeScript types from schema
- Migration pattern: Create new files in `supabase/migrations/` for schema changes

## Row Level Security (RLS) Patterns
```sql
-- Organization-based isolation
CREATE POLICY "Users can only see their organization's data" ON patients
FOR ALL USING (
  organization_id IN (
    SELECT organization_id FROM user_roles 
    WHERE user_id = auth.uid()
  )
  OR EXISTS (
    SELECT 1 FROM user_roles 
    WHERE user_id = auth.uid() 
    AND role = 'system_admin'
  )
);
```

## Query Patterns
```typescript
// Basic query with organization filter
let query = supabase
  .from('patients')
  .select('*')
  .eq('organization_id', organization.id);

// System admin "All Organizations" view
if (organization.id !== 'system-admin-no-org') {
  query = query.eq('organization_id', organization.id);
}

// Joins with select
.select(`
  *,
  appointments:appointments(id, appointment_date, status),
  organization:organizations(id, name)
`)
```

## Performance Optimization
- Use `{ count: 'exact' }` only when needed for pagination
- Limit joins to essential fields only
- Order by indexed columns (`created_at`, `id`)
- Use single queries instead of multiple round trips

## Error Handling
```typescript
const { data, error } = await supabase.from('table').select('*');
if (error) throw new Error(error.message);
```

## Authentication Integration
- Auth state managed by [apps/web/src/contexts/AuthContext.tsx](mdc:apps/web/src/contexts/AuthContext.tsx)
- RLS automatically uses `auth.uid()` for user identification
- Session management with automatic refresh

## Common Gotchas
- Complex joins may cause SQL parsing errors - simplify when needed
- Foreign key references in select must match actual schema relationships
- RLS policies must account for system admin access patterns
- Always handle potential null values from database queries

---
description:
globs:
alwaysApply: false
---
# Local Supabase CLI & Database Inspection

## Database Connection
- **Default credentials**: Username `postgres`, Password `postgres`
- **Database**: `postgres`
- **Port**: `54321` (local Supabase instance)

## Essential CLI Commands

### Database Access
```bash
# Connect to local database (use | cat to avoid pager)
psql -h localhost -p 54321 -U postgres -d postgres | cat

# Alternative connection string format
psql "postgresql://postgres:postgres@localhost:54321/postgres" | cat

# Execute single query
psql -h localhost -p 54321 -U postgres -d postgres -c "SELECT * FROM patients LIMIT 5;" | cat
```

### Supabase Management
```bash
# Start local Supabase stack
supabase start

# Stop local Supabase
supabase stop

# Reset database (drops all data, reapplies migrations)
supabase db reset

# Check status of local services
supabase status

# View logs
supabase logs
```

## Database Inspection Commands

### Schema Exploration
```sql
-- List all tables
\dt

-- Describe table structure
\d patients
\d user_roles
\d organizations

-- List all schemas
\dn

-- List all functions
\df

-- List all policies (RLS)
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE schemaname = 'public';
```

### Data Inspection
```sql
-- Check patient data
SELECT id, first_name, last_name, organization_id, created_at 
FROM patients 
ORDER BY created_at DESC 
LIMIT 10;

-- Check user roles
SELECT user_id, role, organization_id 
FROM user_roles;

-- Check organizations
SELECT id, name, type, owner_id, created_at 
FROM organizations;

-- Check RLS policies on patients table
SELECT * FROM pg_policies WHERE tablename = 'patients';

-- Test organization isolation
SELECT p.first_name, p.last_name, o.name as org_name 
FROM patients p 
JOIN organizations o ON p.organization_id = o.id;
```

### Migration & Schema Management
```bash
# Apply pending migrations
supabase db push

# Create new migration
supabase migration new migration_name

# Generate types after schema changes
supabase gen types typescript --local --schema public > packages/supabase-types/src/generated-types.ts

# Diff current schema vs migrations
supabase db diff

# Dump current schema
supabase db dump --local
```

## Debugging RLS Issues
```sql
-- Check current user context (should be anon or authenticated user)
SELECT current_user, session_user;

-- Test RLS policy for specific user
SET LOCAL rls.user_id = 'user-uuid-here';
SELECT * FROM patients WHERE organization_id = 'org-uuid';

-- Disable RLS temporarily for testing (as superuser)
ALTER TABLE patients DISABLE ROW LEVEL SECURITY;
-- Re-enable after testing
ALTER TABLE patients ENABLE ROW LEVEL SECURITY;

-- Check if RLS is enabled on tables
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public';
```

## Performance Analysis
```sql
-- Check query execution plans
EXPLAIN ANALYZE SELECT * FROM patients WHERE organization_id = 'uuid';

-- Check table sizes
SELECT 
  schemaname,
  tablename,
  pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'public'
ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;

-- Check index usage
SELECT 
  schemaname,
  tablename,
  indexname,
  idx_scan as index_scans,
  idx_tup_read as tuples_read,
  idx_tup_fetch as tuples_fetched
FROM pg_stat_user_indexes 
ORDER BY idx_scan DESC;
```

## Common Troubleshooting

### Connection Issues
```bash
# Check if Supabase is running
supabase status

# Restart if needed
supabase stop && supabase start

# Check Docker containers
docker ps | grep supabase
```

### Data Issues
```sql
-- Check for orphaned records
SELECT p.id, p.organization_id 
FROM patients p 
LEFT JOIN organizations o ON p.organization_id = o.id 
WHERE o.id IS NULL;

-- Verify user roles setup
SELECT ur.user_id, ur.role, ur.organization_id, o.name 
FROM user_roles ur 
LEFT JOIN organizations o ON ur.organization_id = o.id;
```

### Quick Data Seeding
```sql
-- Insert test organization
INSERT INTO organizations (id, name, type, owner_id) 
VALUES ('test-org-1', 'Test Clinic', 'healthcare', 'system');

-- Insert test user role
INSERT INTO user_roles (user_id, role, organization_id) 
VALUES ('user-uuid', 'org_admin', 'test-org-1');

-- Insert test patient
INSERT INTO patients (first_name, last_name, organization_id, date_of_birth, gender) 
VALUES ('John', 'Doe', 'test-org-1', '1990-01-01', 'male');
```

## Pro Tips
- Always use `| cat` with psql to avoid pager issues in terminal
- Use `\q` to quit psql session
- Use `\?` for help within psql
- Check `supabase/seed.sql` for existing test data
- Use `supabase db reset` to start fresh with clean data
- Monitor the local dashboard at `http://localhost:54323` for GUI access
